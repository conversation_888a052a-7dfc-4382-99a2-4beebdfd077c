<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>5‑Cycle Shift Rota</title>
<style>
  :root{
    --ink:#0f172a; --muted:#475569; --line:#d1d5db; --paper:#f9fafb;
    --blue:#dbeafe; --head:#e5e7eb; --accent:#3b82f6; --accent-dark:#2563eb;
    --holiday:#86efac; --weekend:#fff9c4; --offset:#f3f4f6;
  }
  body{font-family:Arial,Helvetica,sans-serif;background:var(--paper);color:var(--ink);margin:20px}
  h1{font-size:20px;margin:0 0 4px 0}
  h2{font-size:16px;margin:0 0 12px 0;color:#444}
  .controls{display:flex;flex-wrap:wrap;gap:8px;align-items:end;margin-bottom:12px}
  .group{border:1px solid var(--line);border-radius:10px;padding:8px 10px;background:#fff}
  .group label{font-size:12px;color:var(--muted)}
  .pattern-input{width:220px;margin-left:6px}
  button{background:var(--accent);color:#fff;border:none;padding:6px 12px;border-radius:8px;cursor:pointer;box-shadow:0 2px 4px rgba(0,0,0,.08)}
  button:hover{background:var(--accent-dark)}

  table{border-collapse:collapse;margin:0 auto;box-shadow:0 6px 14px rgba(0,0,0,.08);background:#fff}
  th,td{border:1px solid #cbd5e1;text-align:center;font-size:12px;padding:2px;min-width:22px}
  th{background:var(--head)}
  .shift-row th{background:#bfdbfe}
  .shift-row td{background:var(--blue);font-weight:bold;letter-spacing:.4px}
  .day{height:22px}

  /* PDF-specific styles for better scaling */
  .pdf-export table{width:100%;font-size:16px}
  .pdf-export th,.pdf-export td{padding:6px;min-width:35px;font-size:14px}
  .pdf-export .day{height:35px}
  .pdf-export h1{font-size:24px;margin-bottom:8px}
  .pdf-export h2{font-size:18px;margin-bottom:12px}
  .offset{background:var(--offset)}
  .fri,.sat{background:var(--weekend)!important}
  .is-holiday{background:var(--holiday)!important}
  .clickable{cursor:pointer}
  .sign-block{max-width:1100px;margin:10px auto 0 auto;font-size:12px;color:var(--muted)}
  .sign-row{display:flex;gap:28px;justify-content:space-between;margin-top:10px}
  .sign-item{flex:1}
  .line{border-bottom:1px solid #aaa;height:22px}
</style>
</head>
<body>
  <h1>5‑Cycle Shift Rota</h1>
  <h2 id="yearHeader">Year: 2026</h2>

  <div class="controls">
    <div class="group">
      <label>Year<br/>
        <input type="number" id="year" value="2026" min="2000" max="2100" />
      </label>
      <button id="btnYear">Load</button>
    </div>

    <div class="group">
      <label>Holiday (click cells or add here)<br/>
        <input type="date" id="holidayDate" />
      </label>
      <button id="btnAddHoliday">Add</button>
      <button id="btnClearYear">Clear Year</button>
    </div>

    <div class="group" style="min-width:340px">
      <label>Edit Patterns (comma or space separated)</label><br/>
      <div id="patternInputs"></div>
      <button id="btnReset">Reset Patterns</button>
    </div>

    <div class="group">
      <button id="btnPdf">Export to PDF</button>
      <div id="pdfHint" style="font-size:11px;color:var(--muted);margin-top:6px">PDF engine: <span id="pdfStatus">loading…</span></div>
    </div>
  </div>

  <div id="rota"></div>
  <div id="signArea" class="sign-block"></div>

  <!-- External PDF library (non-blocking). We'll still lazy‑load in JS if this fails. -->
  <script id="html2pdf-cdn" defer src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.2/html2pdf.bundle.min.js"
          onload="document.dispatchEvent(new Event('html2pdf-ready'))"
          onerror="document.dispatchEvent(new Event('html2pdf-error'))"></script>

  <script>
  /* ====================== Data & Persistence ====================== */
  const defaultPatterns = {
    'A-Shift':['A','N','O','O','M'],
    'B-Shift':['N','O','O','M','A'],
    'C-Shift':['M','A','N','O','O'],
    'D-Shift':['O','M','A','N','O'],
    'E-Shift':['O','O','M','A','N']
  };
  const state = {
    patterns: JSON.parse(localStorage.getItem('patterns') || JSON.stringify(defaultPatterns)),
    holidays: JSON.parse(localStorage.getItem('holidays') || '{}'),
    year: 2026
  };
  const save = () => {
    localStorage.setItem('patterns', JSON.stringify(state.patterns));
    localStorage.setItem('holidays', JSON.stringify(state.holidays));
  };

  /* ====================== Utils ====================== */
  const daysInMonth = (y,m)=> new Date(y, m+1, 0).getDate();
  const fmtDate = (y,m,d)=> `${y}-${String(m+1).padStart(2,'0')}-${String(d).padStart(2,'0')}`;
  const weekday = (y,m,d)=> new Date(y,m,d).getDay(); // 0 Sun..6 Sat
  const gcd = (a,b)=> b ? gcd(b, a%b) : Math.abs(a||1);
  const lcm = (a,b)=> Math.abs(a*b)/gcd(a,b);
  const cycleLength = ()=> {
    const lens = Object.values(state.patterns).map(p=>p.length).filter(n=>n>0);
    if(!lens.length) return 1; let L = lens.reduce((acc,n)=> lcm(acc,n), 1); return Math.min(L,60);
  };
  const dayIndexFromBaseline = (date)=> {
    const base = new Date(2026,0,1); // 2026‑01‑01 is cycle day 0
    return Math.floor((date-base)/(1000*60*60*24));
  };
  const monthIndexForRow = (idx)=> {
    // Table rows: 0..5 months, 6..10 shifts, 11..16 months
    if(idx<=5) return idx;        // Jan..Jun -> 0..5
    if(idx>=11) return idx-5;     // Jul..Dec -> 6..11
    return null;                  // shift rows
  };

  /* ====================== DOM Refs ====================== */
  const rota = document.getElementById('rota');
  const yearInput = document.getElementById('year');
  const yearHeader = document.getElementById('yearHeader');
  const btnYear = document.getElementById('btnYear');
  const holidayDate = document.getElementById('holidayDate');
  const btnAddHoliday = document.getElementById('btnAddHoliday');
  const btnClearYear = document.getElementById('btnClearYear');
  const patternInputs = document.getElementById('patternInputs');
  const btnReset = document.getElementById('btnReset');
  const btnPdf = document.getElementById('btnPdf');
  const pdfStatus = document.getElementById('pdfStatus');
  const signArea = document.getElementById('signArea');

  /* ====================== PDF Loader (robust) ====================== */
  let html2pdfReady = !!window.html2pdf;
  const ensureHtml2Pdf = (()=>{
    let promise = null;
    return function(){
      if(window.html2pdf){ html2pdfReady = true; pdfStatus.textContent = 'ready'; return Promise.resolve(); }
      if(promise) return promise;
      promise = new Promise((resolve,reject)=>{
        const cdn = document.getElementById('html2pdf-cdn');
        if(cdn && cdn.getAttribute('data-tried')){ /* already tried and failed */ }
        else if(cdn){ cdn.setAttribute('data-tried','1'); }
        const s=document.createElement('script');
        s.src='https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.2/html2pdf.bundle.min.js';
        s.onload=()=>{ html2pdfReady=true; pdfStatus.textContent='ready'; resolve(); };
        s.onerror=()=>{ pdfStatus.textContent='failed to load'; reject(new Error('html2pdf failed to load')); };
        document.head.appendChild(s);
      });
      return promise;
    };
  })();
  document.addEventListener('html2pdf-ready',()=>{ html2pdfReady=true; pdfStatus.textContent='ready'; });
  document.addEventListener('html2pdf-error',()=>{ html2pdfReady=false; pdfStatus.textContent='retrying on click…'; });

  /* ====================== Build UI ====================== */
  function buildPatternInputs(){
    patternInputs.innerHTML='';
    Object.keys(state.patterns).forEach(shift=>{
      const wrap=document.createElement('div');
      const lbl=document.createElement('label'); lbl.textContent=shift+': ';
      const input=document.createElement('input'); input.type='text'; input.className='pattern-input';
      input.value=state.patterns[shift].join(',');
      input.addEventListener('change', ()=>{
        const vals=input.value.replace(/\s+/g, ',').split(',').map(v=>v.trim().toUpperCase()).filter(Boolean);
        if(vals.length) state.patterns[shift]=vals; save(); render();
      });
      wrap.appendChild(lbl); wrap.appendChild(input); patternInputs.appendChild(wrap);
    });
  }

  function signatureHTML(y){
    // Signature function kept for reference but not used in PDF
    return "";
  }

  function render(){
    const y = parseInt(yearInput.value,10); state.year = y; yearHeader.textContent = `Year: ${y}`;
    rota.innerHTML='';

    const tbl=document.createElement('table');
    const head=document.createElement('tr');
    head.innerHTML='<th>Month</th>'+Array.from({length:31},(_,i)=>`<th>${i+1}</th>`).join('');
    tbl.appendChild(head);

    const rows=['January','February','March','April','May','June','A-Shift','B-Shift','C-Shift','D-Shift','E-Shift','July','August','September','October','November','December'];
    rows.forEach((label,idx)=>{
      const tr=document.createElement('tr');
      if(label.includes('Shift')) tr.className='shift-row';
      const th=document.createElement('th'); th.textContent=label; tr.appendChild(th);

      const mIdx=monthIndexForRow(idx);
      if(mIdx!==null){
        const days=daysInMonth(y,mIdx);
        const cycLen=cycleLength();
        const offset=((dayIndexFromBaseline(new Date(y,mIdx,1))%cycLen)+cycLen)%cycLen;
        for(let col=1; col<=31; col++){
          const td=document.createElement('td'); td.className='day';
          if(col<=offset || col>offset+days){ td.classList.add('offset'); tr.appendChild(td); continue; }
          const d=col-offset; const wd=weekday(y,mIdx,d);
          td.textContent=d; td.dataset.date=fmtDate(y,mIdx,d); td.classList.add('clickable');
          if(wd===5||wd===6) td.classList.add(wd===5?'fri':'sat');
          if(state.holidays[td.dataset.date]) td.classList.add('is-holiday');
          td.addEventListener('click', (e)=>{
            const key=e.currentTarget.dataset.date; if(state.holidays[key]) delete state.holidays[key]; else state.holidays[key]=true; save(); render();
          });
          tr.appendChild(td);
        }
      }else{
        // Shift pattern band: show repeating pattern across 31 cells
        const pat=state.patterns[label];
        for(let col=1; col<=31; col++){
          const td=document.createElement('td'); td.textContent=pat[(col-1)%pat.length]; tr.appendChild(td);
        }
      }
      tbl.appendChild(tr);
    });

    rota.appendChild(tbl);
    signArea.innerHTML = signatureHTML(y);
  }

  /* ====================== PDF Export ====================== */
  async function downloadPDF(){
    try{ await ensureHtml2Pdf(); }catch(err){ alert('PDF library failed to load. Please check your internet connection and try again.'); return; }
    const y=state.year; const element=rota.cloneNode(true);
    const wrapper=document.createElement('div');
    wrapper.className='pdf-export';
    wrapper.style.padding='8px';
    const hdr=document.createElement('div'); hdr.style.textAlign='center'; hdr.innerHTML=`<h1>SHUAIBA POWER AND WATER PRODUCTION STATIONS</h1><h2>SHIFT SCHEDULE FOR OPERATION STAFF DURING YEAR ${y}</h2>`; wrapper.appendChild(hdr);
    wrapper.appendChild(element);
    // Removed footer with Prepared by, Checked by, and Approved by

    const opt={ margin:0.2, filename:`shift_rota_${y}.pdf`, image:{type:'jpeg',quality:0.98}, html2canvas:{scale:2.5, useCORS:true, scrollX:0, scrollY:0}, jsPDF:{unit:'mm',format:'a4',orientation:'landscape'}};
    window.html2pdf().set(opt).from(wrapper).save();
  }

  /* ====================== Wire up ====================== */
  btnYear.addEventListener('click', render);
  btnAddHoliday.addEventListener('click', ()=>{ const d=holidayDate.value; if(d){ state.holidays[d]=true; save(); render(); }});
  btnClearYear.addEventListener('click', ()=>{ const prefix=state.year+'-'; for(const k of Object.keys(state.holidays)) if(k.startsWith(prefix)) delete state.holidays[k]; save(); render(); });
  btnReset.addEventListener('click', ()=>{ state.patterns=JSON.parse(JSON.stringify(defaultPatterns)); save(); render(); buildPatternInputs(); });
  btnPdf.addEventListener('click', downloadPDF);
  yearInput.addEventListener('change', ()=>{ state.year=parseInt(yearInput.value,10); });
  buildPatternInputs(); render();

  /* ====================== Self‑tests (console) ====================== */
  (function selfTests(){
    console.log('%cRunning rota self‑tests…','color:#0ea5e9');
    console.assert(cycleLength()===5,'Default cycle length should be 5');
    // February 2026 should offset by 31 % 5 = 1
    const febOffset = ((dayIndexFromBaseline(new Date(2026,1,1))%cycleLength())+cycleLength())%cycleLength();
    console.assert(febOffset===1,'Expected Feb 2026 offset = 1, got',febOffset);
    // Pattern parse test
    const sample='m a n o o'; const parsed = sample.replace(/\s+/g, ',').split(',').map(v=>v.trim().toUpperCase()).filter(Boolean);
    console.assert(parsed.join(',')==='M,A,N,O,O','Pattern parser should accept spaces');
    console.log('%cSelf‑tests complete','color:#16a34a');
  })();
  </script>
</body>
</html>
